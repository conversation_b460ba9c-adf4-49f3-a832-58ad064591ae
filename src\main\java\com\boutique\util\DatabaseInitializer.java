// src/main/java/com/boutique/util/DatabaseInitializer.java
package com.boutique.util;

import com.boutique.dao.impl.ClientDAOImpl;
import com.boutique.dao.impl.CommandeDAOImpl;
import com.boutique.dao.impl.FournisseurDAOImpl;
import com.boutique.dao.impl.ProduitDAOImpl;
import com.boutique.dao.impl.UtilisateurDAOImpl;
import com.boutique.model.Client;
import com.boutique.model.Cmd_prd_pivot;
import com.boutique.model.Commande;
import com.boutique.model.Fournisseur;
import com.boutique.model.Produit;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import java.sql.Connection;
import java.sql.Statement;
import java.time.LocalDate;
import java.util.List;

public class DatabaseInitializer {

    private final ProduitDAOImpl     pdao   = new ProduitDAOImpl();
    private final ClientDAOImpl      cdao   = new ClientDAOImpl();
    private final FournisseurDAOImpl fdao   = new FournisseurDAOImpl();
    private final UtilisateurDAOImpl udao   = new UtilisateurDAOImpl();
    private final CommandeDAOImpl    cmdDao = new CommandeDAOImpl();

    public boolean initializeDatabase() {
        try (Connection conn = DBConnection.getConnection();
             Statement  st   = conn.createStatement()) {

            // 1) create & use schema
            st.execute("CREATE DATABASE IF NOT EXISTS boutique");
            st.execute("USE boutique");

            // 2) create tables with column names matching your DAOs
            st.execute("""
                CREATE TABLE IF NOT EXISTS fournisseur (
                  idFournisseur INT AUTO_INCREMENT PRIMARY KEY,
                  nom       VARCHAR(100) NOT NULL,
                  contact   VARCHAR(100)
                ) ENGINE=InnoDB;
                """);
            st.execute("""
                CREATE TABLE IF NOT EXISTS client (
                  idClient   INT AUTO_INCREMENT PRIMARY KEY,
                  nom        VARCHAR(50),
                  prenom     VARCHAR(50),
                  adresse    VARCHAR(100),
                  telephone  VARCHAR(20),
                  email      VARCHAR(100)
                ) ENGINE=InnoDB;
                """);
            st.execute("""
                CREATE TABLE IF NOT EXISTS utilisateur (
                  idUtilisateur  INT AUTO_INCREMENT PRIMARY KEY,
                  nomUtilisateur VARCHAR(50) UNIQUE,
                  motDePasse     VARCHAR(100),
                  role           VARCHAR(20)
                ) ENGINE=InnoDB;
                """);
            st.execute("""
                CREATE TABLE IF NOT EXISTS produit (
                  idProduit      INT AUTO_INCREMENT PRIMARY KEY,
                  nom            VARCHAR(100),
                  description    TEXT,
                  prix           DOUBLE,
                  quantiteStock  INT,
                  categorie      VARCHAR(50),
                  fournisseurId  INT,
                  picture        VARCHAR(255),
                  FOREIGN KEY (fournisseurId)
                    REFERENCES fournisseur(idFournisseur)
                    ON DELETE SET NULL
                ) ENGINE=InnoDB;
                """);
            st.execute("""
                CREATE TABLE IF NOT EXISTS commande (
                  idCommande   INT AUTO_INCREMENT PRIMARY KEY,
                  dateCommande DATE,
                  client_id    INT,
                  created_by   VARCHAR(50),
                  statut       VARCHAR(20),
                  FOREIGN KEY (client_id)
                    REFERENCES client(idClient)
                    ON DELETE CASCADE
                ) ENGINE=InnoDB;
                """);
            st.execute("""
                CREATE TABLE IF NOT EXISTS commande_produit (
                  commande_id INT,
                  produit_id  INT,
                  quantite    INT,
                  PRIMARY KEY (commande_id, produit_id),
                  FOREIGN KEY (commande_id)
                    REFERENCES commande(idCommande)
                    ON DELETE CASCADE,
                  FOREIGN KEY (produit_id)
                    REFERENCES produit(idProduit)
                    ON DELETE CASCADE
                ) ENGINE=InnoDB;
                """);

            // 3) seed only if empty
            seedData();
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(null,
                    "Erreur BD : " + e.getMessage(),
                    "Erreur", JOptionPane.ERROR_MESSAGE);
            return false;
        }
    }

    private void seedData() {
        if (!cdao.findAll().isEmpty()) return;

        seedFournisseurs();
        seedClients();
        seedUtilisateurs();
        seedProduits();
        seedCommandes();
    }

    private void seedFournisseurs() {
        List<Fournisseur> list = List.of(
                new Fournisseur(0, "Atlas Export",           "<EMAIL>"),
                new Fournisseur(0, "Casablanca Traders",     "<EMAIL>"),
                new Fournisseur(0, "Marrakech Handicrafts",  "<EMAIL>"),
                new Fournisseur(0, "Rabat Ceramiques",       "<EMAIL>"),
                new Fournisseur(0, "Tangier Spices Co.",     "<EMAIL>"),
                new Fournisseur(0, "Agadir Oil Mill",        "<EMAIL>"),
                new Fournisseur(0, "Fès Leather Works",      "<EMAIL>"),
                new Fournisseur(0, "Meknès Carpets",         "<EMAIL>")
        );
        list.forEach(fdao::insert);
        System.out.println("• Fournisseurs : " + list.size());
    }

    private void seedClients() {
        List<Client> list = List.of(
                new Client(0, "El Khatib",   "Youssef", "Casablanca",    "060000010", "<EMAIL>"),
                new Client(0, "Benjelloun",  "Sara",    "Rabat",         "060000011", "<EMAIL>"),
                new Client(0, "El Mehdi",    "Nour",    "Fès",           "060000012", "<EMAIL>"),
                new Client(0, "Oulad",       "Hassan",  "Marrakech",     "060000013", "<EMAIL>"),
                new Client(0, "Zahraoui",    "Zahra",   "Tanger",        "060000014", "<EMAIL>"),
                new Client(0, "Choukrane",   "Khalid",  "Agadir",        "060000015", "<EMAIL>"),
                new Client(0, "Benslimane",  "Imane",   "Oujda",         "060000016", "<EMAIL>"),
                new Client(0, "Moussaoui",   "Fatima",  "Meknès",        "060000017", "<EMAIL>")
        );
        list.forEach(cdao::insert);
        System.out.println("• Clients : " + list.size());
    }

    private void seedUtilisateurs() {
        List<Utilisateur> list = List.of(
                new Utilisateur(0, "admin",    "admin",  "ADMIN"),
                new Utilisateur(0, "vendeur1","pass1",  "USER"),
                new Utilisateur(0, "vendeur2","pass2",  "USER")
        );
        list.forEach(udao::insert);
        System.out.println("• Utilisateurs : " + list.size());
    }

    private void seedProduits() {
        List<Produit> list = List.of(
                new Produit(0, "Huile d'Argan 250ml",    "Huile d’argan 100% pure",        350.0,  50, "Beauté",     6, ""),
                new Produit(0, "Thé à la Menthe 250g",   "Pack thé vert & menthe frais",    60.0, 100, "Alimentation",5, ""),
                new Produit(0, "Tajine Céramique 30cm",  "Tajine artisanal décoré",        450.0,  20, "Cuisine",     4, ""),
                new Produit(0, "Babouche Cuir",          "Babouche traditionnel marocain",  220.0,  80, "Chaussure",   7, ""),
                new Produit(0, "Carpet Berbère 2×3m",    "Tapis tissé main de Fès",       1800.0,  10, "Décoration",  8, ""),
                new Produit(0, "Épices Ras el-Hanout",   "Mélange d’épices marocain 100 g", 45.0, 150, "Alimentation",5, ""),
                new Produit(0, "Kaftan Brodé",           "Kaftan femme, broderies argentées",1200.0,15, "Vêtement",    3, ""),
                new Produit(0, "Plateau Zellige",        "Plateau en mosaïque zellige",     380.0,  25, "Décoration",  4, ""),
                new Produit(0, "Savon noir 1 kg",        "Savon d’olive noir traditionnel",  80.0,  60, "Beauté",      1, ""),
                new Produit(0, "Argile Ghassoul 500 g",  "Argile naturelle de l’Atlas",      50.0,  70, "Bien-être",   2, "")
        );
        list.forEach(pdao::insert);
        System.out.println("• Produits : " + list.size());
    }

    private void seedCommandes() {
        var clients  = cdao.findAll();
        var produits = pdao.findAll();
        if (clients.isEmpty() || produits.isEmpty()) return;

        for (int i = 0; i < 5; i++) {
            Client client = clients.get(i % clients.size());
            Cmd_prd_pivot line1 = new Cmd_prd_pivot(produits.get((i*2) % produits.size()),  i + 1);
            Cmd_prd_pivot line2 = new Cmd_prd_pivot(produits.get((i*3) % produits.size()), (i+2));
            Commande cmd = new Commande(
                    0,
                    LocalDate.now().minusDays(5 - i),
                    client,
                    List.of(line1, line2),
                    (i % 2 == 0) ? "en attente" : "expédiée"
            );
            cmd.setCreatedBy("admin");
            cmdDao.insert(cmd);
            System.out.println("• Commande n°" + cmd.getIdCommande() +
                    " pour " + client.getNom() + ": " + cmd.getStatut());
        }
    }

    public static void main(String[] args) {
        if (new DatabaseInitializer().initializeDatabase()) {
            System.out.println("Initialisation terminée !");
        }
        System.exit(0);
    }
}
